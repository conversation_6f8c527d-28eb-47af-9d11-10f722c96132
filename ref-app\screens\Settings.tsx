import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';

const Settings = () => {
  const [startHalftimeAutomatically, setStartHalftimeAutomatically] = useState(true);
  const [resetTimeToZero, setResetTimeToZero] = useState(false);
  const [pushNotification, setPushNotification] = useState(true);
  const [alwaysUsePlayerKeyboard, setAlwaysUsePlayerKeyboard] = useState(false);
  const [alwaysAskGoalType, setAlwaysAskGoalType] = useState(false);

  const timerSettings = [
    { label: 'Timer options', arrow: true },
    { 
      label: 'Start halftime automatically', 
      switch: true, 
      value: startHalftimeAutomatically, 
      onToggle: setStartHalftimeAutomatically 
    },
    { 
      label: 'Rest time to 0 from second half', 
      switch: true, 
      value: resetTimeToZero, 
      onToggle: setResetTimeToZero 
    },
  ];

  const manage = [
    { label: 'Manage your template', arrow: true },
    { label: 'Manage your watch', arrow: true },
    { label: 'Manage misconduct code', arrow: true },
    { 
      label: 'Push notification', 
      switch: true, 
      value: pushNotification, 
      onToggle: setPushNotification 
    },
    { label: 'Currency', value: '£ GBP', arrow: true },
    { label: 'Language', value: '🇬🇧', arrow: true },
  ];

  const preference = [
    { label: 'Save match calendar', arrow: true },
    { label: 'Units of measure', arrow: true },
    { label: 'Time format', value: '24 hours', arrow: true },
    { label: 'Season start date', value: '25-08-2025', arrow: true },
    { label: 'Custom Sprint map options', arrow: true },
    { 
      label: 'Always use player number keyboard', 
      switch: true, 
      value: alwaysUsePlayerKeyboard, 
      onToggle: setAlwaysUsePlayerKeyboard 
    },
    { 
      label: 'Always ask for goal type e.g. free kick, penalty', 
      switch: true, 
      value: alwaysAskGoalType, 
      onToggle: setAlwaysAskGoalType 
    },
  ];

  type SectionItem = { 
    label: string; 
    value?: string | boolean; 
    arrow?: boolean; 
    switch?: boolean; 
    onToggle?: (value: boolean) => void; 
  };

  type SectionProps = { 
    title: string; 
    items: SectionItem[] 
  };

  const Section = ({ title, items }: SectionProps) => (
    <View style={styles.sectionBox}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {items.map((item: SectionItem, idx: number) => (
        <TouchableOpacity
          key={item.label}
          style={[styles.row, idx === items.length - 1 && { borderBottomWidth: 0 }]}
          activeOpacity={item.arrow ? 0.7 : 1}
        >
          <Text style={styles.rowLabel}>{item.label}</Text>
          <View style={styles.rowRight}>
            {item.value && typeof item.value === 'string' && <Text style={styles.rowValue}>{item.value}</Text>}
            {item.switch && item.onToggle && (
              <Switch
                value={item.value as boolean}
                onValueChange={item.onToggle}
                trackColor={{ false: '#e0e0e0', true: '#4CAF50' }}
                thumbColor={'#fff'}
              />
            )}
            {item.arrow && <MaterialIcons name="chevron-right" size={22} color="#222" />}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <Header title="Settings" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Section title="Timer Settings" items={timerSettings} />
        <Section title="Manage" items={manage} />
        <Section title="Preference" items={preference} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    overflow: 'hidden',
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#e0e0e0',
    marginBottom: 18,
    marginTop: 8,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontWeight: '700',
    fontSize: 14,
    color: '#222',
    backgroundColor: '#fff',
    paddingHorizontal: 14,
    paddingTop: 12,
    paddingBottom: 6,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 13,
    paddingHorizontal: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  rowLabel: {
    fontSize: 15,
    color: '#222',
    fontWeight: '500',
    flex: 1,
  },
  rowRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowValue: {
    fontSize: 15,
    color: '#888',
    fontWeight: '500',
    marginRight: 8,
  },
});

export default Settings;