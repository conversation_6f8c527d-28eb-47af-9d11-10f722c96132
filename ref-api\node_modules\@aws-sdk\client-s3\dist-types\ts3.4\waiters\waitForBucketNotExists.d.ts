import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { HeadBucketCommandInput } from "../commands/HeadBucketCommand";
import { S3Client } from "../S3Client";
export declare const waitForBucketNotExists: (
  params: WaiterConfiguration<S3Client>,
  input: HeadBucketCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilBucketNotExists: (
  params: WaiterConfiguration<S3Client>,
  input: HeadBucketCommandInput
) => Promise<WaiterResult>;
