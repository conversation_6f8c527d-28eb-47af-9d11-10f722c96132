{"name": "mobile-app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/native": "^7.1.11", "@react-navigation/native-stack": "^7.3.16", "@react-navigation/stack": "^7.3.4", "@types/react-native-vector-icons": "^6.4.18", "expo": "^53.0.0", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "typescript": "~5.8.3"}, "private": true}