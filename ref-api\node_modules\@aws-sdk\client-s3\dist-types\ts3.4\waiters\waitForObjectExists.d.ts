import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { HeadObjectCommandInput } from "../commands/HeadObjectCommand";
import { S3Client } from "../S3Client";
export declare const waitForObjectExists: (
  params: WaiterConfiguration<S3Client>,
  input: HeadObjectCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilObjectExists: (
  params: WaiterConfiguration<S3Client>,
  input: HeadObjectCommandInput
) => Promise<WaiterResult>;
