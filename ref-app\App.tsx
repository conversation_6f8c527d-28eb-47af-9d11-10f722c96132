import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { createStackNavigator } from '@react-navigation/stack';
import ProfileSetup from './screens/ProfileSetup';
import ConnectWatch from './screens/ConnectWatch';
import Dashboard from './screens/Dashboard';
import Evaluate from './screens/Evaluate';
import Account from './screens/Account';
import Settings from './screens/Settings';
import Integrations from './screens/Integrations';
import CreateMatchScreen from './screens/CreateMatchScreen';
import AppTutorialScreen from './screens/AppTutorialScreen';
import GoProScreen from './screens/GoProScreen';
import SupportScreen from './screens/SupportScreen';
import FAQScreen from './screens/FAQScreen';
import FeatureRequestsScreen from './screens/FeatureRequestsScreen';
import WatchDebuggerScreen from './screens/WatchDebuggerScreen';
import ContactUsScreen from './screens/ContactUsScreen';
import TermsConditionsScreen from './screens/TermsConditionsScreen';
import PrivacyPolicyScreen from './screens/PrivacyPolicyScreen';
import FitnessSyncScreen from './screens/FitnessSyncScreen';
import WhyUsScreen from './screens/WhyUsScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <Stack.Navigator initialRouteName="ProfileSetup" screenOptions={{ headerShown: false }}>
          <Stack.Screen name="ProfileSetup" component={ProfileSetup} />
          <Stack.Screen name="ConnectWatch" component={ConnectWatch} />
          <Stack.Screen name="Dashboard" component={Dashboard} />
          <Stack.Screen name="Account" component={Account} />
          <Stack.Screen name="Settings" component={Settings} />
          <Stack.Screen name="Integrations" component={Integrations} />
          <Stack.Screen name="Evaluate" component={Evaluate} />
          <Stack.Screen 
            name="CreateMatch" 
            component={CreateMatchScreen} 
            options={{
              headerShown: false,
              presentation: 'modal',
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="AppTutorial" 
            component={AppTutorialScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="GoPro" 
            component={GoProScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="Support" 
            component={SupportScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="FAQ" 
            component={FAQScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="FeatureRequests" 
            component={FeatureRequestsScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="WatchDebugger" 
            component={WatchDebuggerScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="ContactUs" 
            component={ContactUsScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="TermsConditions" 
            component={TermsConditionsScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="PrivacyPolicy" 
            component={PrivacyPolicyScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="FitnessSync" 
            component={FitnessSyncScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
          <Stack.Screen 
            name="WhyUs" 
            component={WhyUsScreen} 
            options={{
              headerShown: false,
              cardStyle: { backgroundColor: 'white' }
            }} 
          />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
