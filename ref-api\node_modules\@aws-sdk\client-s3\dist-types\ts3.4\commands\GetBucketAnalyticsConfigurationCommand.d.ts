import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketAnalyticsConfigurationOutput,
  GetBucketAnalyticsConfigurationRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketAnalyticsConfigurationCommandInput
  extends GetBucketAnalyticsConfigurationRequest {}
export interface GetBucketAnalyticsConfigurationCommandOutput
  extends GetBucketAnalyticsConfigurationOutput,
    __MetadataBearer {}
declare const GetBucketAnalyticsConfigurationCommand_base: {
  new (
    input: GetBucketAnalyticsConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketAnalyticsConfigurationCommandInput,
    GetBucketAnalyticsConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketAnalyticsConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketAnalyticsConfigurationCommandInput,
    GetBucketAnalyticsConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketAnalyticsConfigurationCommand extends GetBucketAnalyticsConfigurationCommand_base {
  protected static __types: {
    api: {
      input: GetBucketAnalyticsConfigurationRequest;
      output: GetBucketAnalyticsConfigurationOutput;
    };
    sdk: {
      input: GetBucketAnalyticsConfigurationCommandInput;
      output: GetBucketAnalyticsConfigurationCommandOutput;
    };
  };
}
