I ran npm install -g @expo/cli@latest   
and nvm use 22.16.0
 then npx create-expo-app@latest mobile-app --template blank-typescript
cd mobile-app

- I did now this npm install @react-navigation/native @react-navigation/stack
npx expo install react-native-screens react-native-safe-area-context
npm install @react-navigation/native-stack

-cmd wsl --install  user is huwaei password is : huwaeiazeroual1

- did this now  npx @nestjs/cli new . --package-manager npm --skip-git

- docker compose -build "maybe not correct"
-npm install prisma @prisma/client
npx prisma init
- ON wsl i did cd then curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
- Then sudo apt-get install -y nodejs build-essential

-

