import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketCorsRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketCorsCommandInput extends PutBucketCorsRequest {}
export interface PutBucketCorsCommandOutput extends __MetadataBearer {}
declare const PutBucketCorsCommand_base: {
  new (
    input: PutBucketCorsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketCorsCommandInput,
    PutBucketCorsCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutBucketCorsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketCorsCommandInput,
    PutBucketCorsCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketCorsCommand extends PutBucketCorsCommand_base {
  protected static __types: {
    api: {
      input: PutBucketCorsRequest;
      output: {};
    };
    sdk: {
      input: PutBucketCorsCommandInput;
      output: PutBucketCorsCommandOutput;
    };
  };
}
