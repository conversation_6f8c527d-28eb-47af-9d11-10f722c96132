import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { RedisService } from './redis/redis.service';
import { S3Service } from './s3/s3.service';

@Injectable()
export class AppService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private s3: S3Service,
  ) {}

  getHello(): string {
    return 'Hello World!';
  }

  // Example method to test database connection
  async testDatabase(): Promise<string> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return 'Database connection successful!';
    } catch (error) {
      return `Database connection failed: ${error.message}`;
    }
  }

  // Example method to test Redis connection
  async testRedis(): Promise<string> {
    try {
      await this.redis.set('test', 'Hello Redis!', 60);
      const value = await this.redis.get('test');
      return `Redis connection successful! Value: ${value}`;
    } catch (error) {
      return `Redis connection failed: ${error.message}`;
    }
  }

  // Example method to test S3/MinIO connection
  async testS3(): Promise<string> {
    try {
      // This will test the S3 client initialization
      const client = this.s3.getClient();
      return 'S3/MinIO client initialized successfully!';
    } catch (error) {
      return `S3/MinIO connection failed: ${error.message}`;
    }
  }
}
