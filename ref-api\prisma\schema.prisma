// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for referees, assessors, admins, etc.
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      UserRole @default(REFEREE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  matches   Match[]
  assessments Assessment[]

  @@map("users")
}

// Match model for tracking games
model Match {
  id          String      @id @default(cuid())
  title       String
  date        DateTime
  venue       String
  status      MatchStatus @default(SCHEDULED)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  referee     User   @relation(fields: [refereeId], references: [id])
  refereeId   String
  assessments Assessment[]

  @@map("matches")
}

// Assessment model for referee evaluations
model Assessment {
  id        String   @id @default(cuid())
  score     Int
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  match     Match  @relation(fields: [matchId], references: [id])
  matchId   String
  assessor  User   @relation(fields: [assessorId], references: [id])
  assessorId String

  @@map("assessments")
}

// Enums
enum UserRole {
  REFEREE
  ASSISTANT_REFEREE
  FOURTH_OFFICIAL
  ASSESSOR
  ADMIN
  MANAGER
}

enum MatchStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
