import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { apiService } from '../api/apiService';

interface TestResult {
  service: string;
  status: 'loading' | 'success' | 'error';
  message: string;
}

export default function ApiTestScreen() {
  const [results, setResults] = useState<TestResult[]>([]);

  const updateResult = (service: string, status: TestResult['status'], message: string) => {
    setResults(prev => {
      const filtered = prev.filter(r => r.service !== service);
      return [...filtered, { service, status, message }];
    });
  };

  const testHealthCheck = async () => {
    updateResult('health', 'loading', 'Testing...');
    try {
      const response = await apiService.healthCheck();
      if (response.error) {
        updateResult('health', 'error', response.error);
      } else {
        updateResult('health', 'success', `Status: ${response.data?.status}`);
      }
    } catch (error) {
      updateResult('health', 'error', 'Connection failed');
    }
  };

  const testDatabase = async () => {
    updateResult('database', 'loading', 'Testing...');
    try {
      const response = await apiService.testDatabase();
      if (response.error) {
        updateResult('database', 'error', response.error);
      } else {
        updateResult('database', 'success', response.data?.result || 'Success');
      }
    } catch (error) {
      updateResult('database', 'error', 'Connection failed');
    }
  };

  const testRedis = async () => {
    updateResult('redis', 'loading', 'Testing...');
    try {
      const response = await apiService.testRedis();
      if (response.error) {
        updateResult('redis', 'error', response.error);
      } else {
        updateResult('redis', 'success', response.data?.result || 'Success');
      }
    } catch (error) {
      updateResult('redis', 'error', 'Connection failed');
    }
  };

  const testS3 = async () => {
    updateResult('s3', 'loading', 'Testing...');
    try {
      const response = await apiService.testS3();
      if (response.error) {
        updateResult('s3', 'error', response.error);
      } else {
        updateResult('s3', 'success', response.data?.result || 'Success');
      }
    } catch (error) {
      updateResult('s3', 'error', 'Connection failed');
    }
  };

  const testAll = async () => {
    await testHealthCheck();
    await testDatabase();
    await testRedis();
    await testS3();
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'loading': return '#FFA500';
      case 'success': return '#4CAF50';
      case 'error': return '#F44336';
      default: return '#666';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>API Connection Tests</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testAll}>
          <Text style={styles.buttonText}>Test All Services</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity style={[styles.button, styles.smallButton]} onPress={testHealthCheck}>
          <Text style={styles.buttonText}>Health</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.smallButton]} onPress={testDatabase}>
          <Text style={styles.buttonText}>Database</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity style={[styles.button, styles.smallButton]} onPress={testRedis}>
          <Text style={styles.buttonText}>Redis</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.smallButton]} onPress={testS3}>
          <Text style={styles.buttonText}>S3/MinIO</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {results.map((result, index) => (
          <View key={index} style={styles.resultItem}>
            <View style={styles.resultHeader}>
              <Text style={styles.serviceName}>{result.service.toUpperCase()}</Text>
              <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(result.status) }]} />
            </View>
            <Text style={styles.resultMessage}>{result.message}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  smallButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  buttonRow: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    marginTop: 30,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  resultItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  resultMessage: {
    fontSize: 14,
    color: '#666',
  },
});
